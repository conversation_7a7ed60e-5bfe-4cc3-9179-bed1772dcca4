# 文件流上传功能使用说明

## 概述

新增了 `uploadFileStream` 方法，支持使用 PUT 请求直接上传文件流到预签名 URL。这种方式主要用于对象存储服务（如阿里云 OSS、AWS S3 等）的预签名 URL 上传场景。

## 功能特点

- 使用 PUT 请求直接上传文件流
- 支持自定义 Content-Type
- 适用于预签名 URL 上传场景
- 支持上传进度监控
- 自动处理多种成功状态码（200、201、204）

## API 接口

### VAxios.uploadFileStream

```typescript
uploadFileStream<T = any>(
  config: AxiosRequestConfig, 
  params: UploadFileStreamParams
): Promise<T>
```

#### 参数说明

- `config`: Axios 请求配置
  - `url`: 请求 URL（通常为空字符串，使用 baseURL）
  - `baseURL`: 预签名的完整 URL
  - `onUploadProgress`: 上传进度回调函数
  - `headers`: 自定义请求头（可选）
  
- `params`: 上传参数
  - `file`: 要上传的文件或 Blob 对象
  - `contentType`: 文件的 Content-Type（可选，会自动从文件中获取）

### 封装的 API 函数

```typescript
apiUploadFileToSignedUrl(
  signedUrl: string,
  file: File | Blob,
  contentType?: string,
  config?: AxiosRequestConfig
): Promise<any>
```

## 使用示例

### 基础使用

```typescript
import { defHttp } from '@/utils/http/axios';

// 方式1：直接使用 uploadFileStream 方法
const uploadFile = async (signedUrl: string, file: File) => {
  try {
    const response = await defHttp.uploadFileStream(
      {
        url: '',
        baseURL: signedUrl,
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
            console.log(`上传进度: ${progress}%`);
          }
        }
      },
      {
        file,
        contentType: 'application/octet-stream' // 可选，会自动从文件中获取
      }
    );
    
    console.log('上传成功:', response);
  } catch (error) {
    console.error('上传失败:', error);
  }
};
```

### 使用封装的 API 函数

```typescript
import { apiUploadFileToSignedUrl, apiGetUploadUrl } from '@/api/admin/file';

const uploadFileWithSignedUrl = async (file: File) => {
  try {
    // 第一步：获取预签名URL
    const uploadUrlRes = await apiGetUploadUrl({
      objectName: file.name,
      contentType: file.type,
    });
    
    // 第二步：使用预签名URL上传文件
    const response = await apiUploadFileToSignedUrl(
      uploadUrlRes.url,
      file,
      file.type,
      {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
            console.log(`上传进度: ${progress}%`);
          }
        }
      }
    );
    
    console.log('上传成功:', response);
  } catch (error) {
    console.error('上传失败:', error);
  }
};
```

### 在 Vue 组件中使用

```vue
<template>
  <div>
    <input type="file" @change="handleFileChange" />
    <button @click="uploadFile" :disabled="!selectedFile || uploading">
      {{ uploading ? `上传中 ${progress}%` : '上传文件' }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { apiUploadFileToSignedUrl, apiGetUploadUrl } from '@/api/admin/file';
import { message } from 'ant-design-vue';

const selectedFile = ref<File | null>(null);
const uploading = ref(false);
const progress = ref(0);

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0];
  }
};

const uploadFile = async () => {
  if (!selectedFile.value) return;
  
  try {
    uploading.value = true;
    progress.value = 0;
    
    // 获取预签名URL
    const uploadUrlRes = await apiGetUploadUrl({
      objectName: selectedFile.value.name,
      contentType: selectedFile.value.type,
    });
    
    // 上传文件
    await apiUploadFileToSignedUrl(
      uploadUrlRes.url,
      selectedFile.value,
      selectedFile.value.type,
      {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            progress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
          }
        }
      }
    );
    
    message.success('文件上传成功');
  } catch (error) {
    console.error('上传失败:', error);
    message.error('文件上传失败');
  } finally {
    uploading.value = false;
    progress.value = 0;
  }
};
</script>
```

## 与原有 uploadFile 方法的区别

| 特性 | uploadFile | uploadFileStream |
|------|------------|------------------|
| 请求方式 | POST | PUT |
| 数据格式 | FormData | 文件流 |
| 适用场景 | 传统文件上传接口 | 预签名URL上传 |
| Content-Type | multipart/form-data | application/octet-stream 或文件类型 |
| 额外参数 | 支持表单字段 | 仅支持文件 |

## 注意事项

1. 预签名 URL 通常有时效性，请确保在有效期内使用
2. 不同的对象存储服务可能对 Content-Type 有不同要求
3. 上传成功的状态码可能是 200、201 或 204，方法已自动处理
4. 建议在上传前验证文件大小和类型
5. 网络异常时会自动抛出错误，请做好错误处理

## 类型定义

```typescript
// 文件流上传参数类型
export interface UploadFileStreamParams {
  // 要上传的文件或Blob对象
  file: File | Blob;
  // 文件的Content-Type，如果不指定则自动从文件中获取
  contentType?: string;
}
```
